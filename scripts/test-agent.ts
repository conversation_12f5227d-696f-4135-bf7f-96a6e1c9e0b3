// Load environment variables first
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env.development' });

// Validate API key
if (!process.env.OPENAI_API_KEY) {
  console.error('ERROR: OPENAI_API_KEY is not set in .env.development');
  process.exit(1);
}

// Make sure it's available to openai library directly
process.env.AI_OPENAI_API_KEY = process.env.OPENAI_API_KEY;

import { mastra } from '../src/mastra';

async function testDentalNarratorAgent() {
  console.log('🧪 Testing Dental Narrator Agent...');
  
  try {
    // Get the dental narrator agent
    const agent = mastra.getAgent('dentalNarratorAgent');
    
    if (!agent) {
      console.error('❌ Dental Narrator Agent not found');
      return;
    }
    
    console.log('✅ Agent found, testing with sample chart notes...');
    
    // Test query 1: Simple crown procedure
    console.log('\n📝 Test 1: Crown Procedure for Delta Dental');
    console.log('=' .repeat(50));
    
    const testQuery1 = `Generate a narrative for these dental chart notes for Delta Dental:
    
Patient: <PERSON>, DOB: 01/15/1980
Chief Complaint: "My tooth hurts when I bite down"
Clinical Findings: Tooth #14 (upper left first molar) has large amalgam restoration with recurrent decay. Radiographs show decay extending close to pulp chamber.
Treatment: Crown preparation and temporary crown placement
Procedure Code: D2750 - Crown - porcelain fused to high noble metal`;

    const response1 = await agent.generate(testQuery1);
    console.log('🤖 Agent Response:');
    console.log(response1.text);
    
    // Test query 2: Root canal procedure
    console.log('\n📝 Test 2: Root Canal Procedure for Cigna');
    console.log('=' .repeat(50));
    
    const testQuery2 = `Generate a narrative for these dental chart notes for Cigna:
    
Patient: Sarah Johnson, DOB: 03/22/1975
Chief Complaint: "Severe tooth pain keeping me awake at night"
Clinical Findings: Tooth #19 (lower left first molar) with deep caries, positive percussion test, and periapical radiolucency on radiograph
Treatment: Root canal therapy completed in single visit
Procedure Code: D3330 - Molar endodontic therapy`;

    const response2 = await agent.generate(testQuery2);
    console.log('🤖 Agent Response:');
    console.log(response2.text);
    
    // Test query 3: Preventive care
    console.log('\n📝 Test 3: Preventive Care for General Insurance');
    console.log('=' .repeat(50));
    
    const testQuery3 = `Generate a narrative for these dental chart notes:
    
Patient: Michael Brown, DOB: 07/10/1990
Chief Complaint: "Routine cleaning and checkup"
Clinical Findings: Generalized gingivitis, moderate plaque and calculus buildup, no caries detected
Treatment: Prophylaxis (cleaning) and fluoride treatment
Procedure Codes: D1110 - Adult prophylaxis, D1206 - Fluoride varnish`;

    const response3 = await agent.generate(testQuery3);
    console.log('🤖 Agent Response:');
    console.log(response3.text);
    
    console.log('\n🎉 Agent testing completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing agent:', error);
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Dental Narrator Agent Test...');
  
  try {
    await testDentalNarratorAgent();
  } catch (error) {
    console.error('💥 Agent test failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { testDentalNarratorAgent };
