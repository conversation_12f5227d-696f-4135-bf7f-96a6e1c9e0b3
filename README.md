# Dental Narrator - Insurance Claim Narrative Generator

A specialized RAG-based system for generating dental insurance claim narratives from chart notes. This tool helps dental professionals create accurate, compliant narratives that meet specific insurance carrier requirements.

## Features

- **RAG-based Insurance Guidelines**: Vector database with semantic search for carrier-specific guidelines
- **Intelligent Text Processing**: Extracts key information from dental chart notes
- **Carrier-Specific Formatting**: Tailors narratives to meet requirements for different carriers
- **Character Count Management**: Ensures narratives meet length requirements
- **Medical Necessity Justification**: Explicitly provides treatment justification
- **Attachment Recommendations**: Identifies required attachments based on procedure type

## Technical Architecture

The system uses a Retrieval-Augmented Generation (RAG) approach:

1. **Indexing Phase**: Insurance guidelines are chunked, embedded, and stored in a vector database
2. **Retrieval Phase**: 
   - Extract relevant information from chart notes
   - Query vector database to find relevant guidelines based on carrier and procedure
3. **Generation Phase**:
   - Combine chart information and guidelines in a well-structured prompt
   - Use an LLM to generate a compliant narrative
   - Post-process to ensure character count requirements are met

## Project Structure

```
dental-narrator/
├── src/                    # Main application source code
├── scripts/               # Utility scripts for data management
├── assets/                # CSV data files for import
├── supabase/             # Database migrations and schema
└── web-bundles/          # Web application bundles
```

### Scripts Directory

The `scripts/` folder contains all utility scripts for data management:
- **Data Import**: CSV import and database seeding
- **Data Quality**: Validation and cleaning scripts
- **AI Features**: Embedding generation and testing

See [scripts/README.md](scripts/README.md) for detailed documentation.

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL (for vector storage)
- OpenAI API key

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```
# Create a .env file with:
OPENAI_API_KEY=your_openai_api_key
POSTGRES_CONNECTION_STRING=postgresql://postgres:postgres@localhost:5432/postgres
```

4. Initialize the database:

```bash
npm run init
```

## Usage

### Starting the Server

```bash
npx mastra dev
```

This will start the Mastra server and make your agent available at http://localhost:4111.

### Using the Agent

The dental narrator agent accepts:

1. **Chart Notes**: The dental chart notes to process
2. **Insurance Carrier**: The name of the carrier (optional, defaults to general guidelines)
3. **Procedure Category**: Category of dental procedure (optional)

Example query:
"Generate a narrative for these chart notes for Delta Dental. The procedure is a root canal."

### Example Chart Notes

Here's a sample chart note you can use to test the system:

```
Patient: Jane Doe
Date: 2023-05-15
Chief Complaint: Patient reports severe pain in lower right quadrant, specifically tooth #30. Pain is spontaneous, throbbing, and wakes patient at night. Pain intensifies with hot beverages.

Clinical Findings:
- Visual exam shows large occlusal restoration with distal marginal breakdown
- Tooth #30 is tender to percussion
- Cold test: prolonged, lingering pain
- EPT: hypersensitive response at 4/10
- Radiographic exam: periapical radiolucency approximately 3mm in diameter

Diagnosis: Irreversible pulpitis with symptomatic apical periodontitis on tooth #30

Treatment: Root canal therapy recommended and performed on tooth #30
- Local anesthesia administered (2% lidocaine with 1:100,000 epinephrine)
- Rubber dam isolation
- Access preparation
- Working length determined with apex locator and confirmed radiographically
- Canals instrumented with rotary files
- Irrigation protocol: 5.25% NaOCl, 17% EDTA
- Canals obturated with gutta percha using warm vertical condensation
- Temporary restoration placed with Cavit

Patient tolerated procedure well. Postoperative instructions provided verbally and in writing. Patient scheduled for crown preparation in 2 weeks.

Medical History:
- Controlled hypertension (medication: lisinopril 10mg daily)
- Penicillin allergy (rash)
```

### Sample Output

The agent returns:

- **Narrative**: The generated insurance claim narrative
- **Character Count**: Number of characters in the narrative
- **Required Attachments**: List of attachments needed for the claim
- **Guidelines Followed**: The insurance guidelines that were used
- **Medical History**: Relevant medical history extracted from notes

## Development

To extend or modify the system:

1. Update guideline data in `src/mastra/tools/index.ts`
2. Add new carriers by extending the `insuranceGuidelinesDB` object
3. Customize processing logic in the `dentalNarratorTool` execute function

## License

[MIT License](LICENSE)

## Acknowledgements

This project was built using the [Mastra](https://mastra.ai) agent framework. 