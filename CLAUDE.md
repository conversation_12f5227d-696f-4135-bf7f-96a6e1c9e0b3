# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Dental Narrator is a RAG-based system that generates insurance-compliant narratives for dental procedures using chart notes. It uses the OpenAI Agents SDK for text generation and PostgreSQL with pgvector for storing and retrieving insurance guidelines.

## Development Commands

### Essential Commands
```bash
# Install dependencies
npm install

# Initialize guidelines database (run once after setup)
npm run init

# Start development server with auto-reload
npm run dev

# Test OpenAI agent
npm run test-openai-agent

# Build for production
npm run build

# Run production build
npm start

# Test database connection
npm run test-db
```

### Environment Setup
Create `.env.development` with:
```
OPENAI_API_KEY=your_openai_api_key_here
POSTGRES_CONNECTION_STRING=postgresql://username:password@hostname:port/database
LOG_LEVEL=info  # optional
PORT=4111       # optional
```

## Architecture

### Core Components

1. **OpenAI Agents SDK Integration** (`src/agents/dental-narrator-agent.ts`)
   - Configures OpenAI Agent with tools and instructions
   - Enables RAG and PostgreSQL database features
   - Manages OpenAI integration

2. **Dental Narrator Agent** (`src/agents/dental-narrator-agent.ts`)
   - Main orchestrator for narrative generation
   - Handles insurance carrier selection and guideline retrieval
   - Manages character limit compliance

3. **RAG Tools** (`src/mastra/tools/index.ts`)
   - `dentalNarratorTool`: Main tool that generates narratives from chart notes
   - `initializeGuidelines`: Populates vector database with insurance guidelines
   - `insuranceGuidelinesQueryTool`: Retrieves relevant guidelines based on carrier/procedure

4. **Vector Database Schema**
   - Table: `dental_insurance_guidelines`
   - Stores chunked insurance guidelines with embeddings
   - Enables semantic search for relevant policies

### Narrative Generation Flow

1. Extract key information from chart notes (symptoms, diagnosis, procedures)
2. Query vector database for carrier-specific guidelines
3. Generate narrative combining clinical information with compliance requirements
4. Validate character count and adjust if needed
5. Return narrative with metadata (character count, attachments, guidelines)

### Supported Insurance Carriers
- Default (generic guidelines)
- Delta Dental
- Cigna Dental

## Development Workflow

Per `.cursor/rules/development-workflow.mdc`, follow these steps:

1. **Requirement Analysis**: Use Sequential Thinking MCP Tool to understand requirements
2. **Planning**: Create detailed implementation plan before coding
3. **Mastra Integration**: Always use Mastra MCP tool before writing Mastra-specific code
4. **Implementation**: Follow step-by-step approach with iterative testing
5. **Testing**: Test each component as you build

## Important Notes

- The project uses TypeScript with strict mode enabled
- No existing test suite - consider adding tests for tools and workflows
- Character limits are carrier-specific and automatically enforced
- Guidelines are pre-loaded during initialization (`npm run init`)
- The server runs on port 4111 by default (configurable via PORT env var)
- OpenAI Agents SDK provides programmatic access to the dental narrator functionality

## Common Development Tasks

### Adding New Insurance Carriers
1. Add carrier data to the `@Parsed` directory or database seeding scripts
2. Update carrier lookup tools in `src/agents/dental-narrator-agent.ts` if needed
3. Re-run `npm run seed-database` to populate database with new guidelines

### Modifying Narrative Generation Logic
- Core logic in `dentalNarratorTool` function
- Adjust prompts or validation logic as needed
- Test with various chart note formats

### Database Operations
- Connection string must include `?sslmode=require` for Supabase
- pgvector extension required for embeddings
- Use `npm run test-db` to verify connectivity