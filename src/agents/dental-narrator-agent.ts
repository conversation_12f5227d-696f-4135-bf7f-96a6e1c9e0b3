import { Agent, tool, run } from '@openai/agents';
import { z } from 'zod';
import { Client } from 'pg';
import OpenAI from 'openai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Initialize OpenAI for embeddings
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Create PostgreSQL client
const createClient = () => new Client({
  connectionString: process.env.POSTGRES_CONNECTION_STRING,
  ssl: { rejectUnauthorized: false }
});

// Tool: Search dental guidelines using vector similarity
const searchGuidelinesTool = tool({
  name: 'search_dental_guidelines',
  description: 'Search for relevant dental insurance guidelines using semantic similarity. Use this when you need specific coverage information, procedure requirements, or documentation guidelines.',
  parameters: z.object({
    query: z.string().describe('The search query describing what dental information is needed'),
    carrier: z.string().nullable().optional().describe('Specific insurance carrier name (e.g., "Delta Dental", "Cigna")'),
    category: z.string().nullable().optional().describe('Procedure category (e.g., "restorative", "preventive", "endodontic")'),
    limit: z.number().default(5).describe('Maximum number of guidelines to return')
  }),
  execute: async ({ query, carrier, category, limit }) => {
    console.log(`🔍 Searching guidelines: "${query}" | Carrier: ${carrier || 'Any'} | Category: ${category || 'Any'}`);
    
    try {
      // Generate embedding for the query
      const embeddingResponse = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: query,
      });
      
      const queryEmbedding = embeddingResponse.data[0].embedding;
      
      // Search using our optimized vector search function
      const client = createClient();
      await client.connect();
      
      try {
        const result = await client.query(`
          SELECT 
            g.id,
            g.title,
            g.category,
            g.content,
            ic.carrier_name,
            (1 - (e.embedding <=> $1::vector)) as similarity_score
          FROM embeddings e
          JOIN guidelines g ON e.content_id = g.id
          LEFT JOIN insurance_carriers ic ON g.carrier_id = ic.id
          WHERE 
            e.content_type = 'guideline'
            AND (1 - (e.embedding <=> $1::vector)) > 0.7
            AND ($2::text IS NULL OR LOWER(ic.carrier_name) LIKE LOWER('%' || $2 || '%'))
            AND ($3::text IS NULL OR LOWER(g.category) LIKE LOWER('%' || $3 || '%'))
          ORDER BY e.embedding <=> $1::vector
          LIMIT $4;
        `, [JSON.stringify(queryEmbedding), carrier, category, limit]);
        
        const guidelines = result.rows.map(row => ({
          id: row.id,
          title: row.title,
          category: row.category,
          carrier: row.carrier_name,
          similarity: (row.similarity_score * 100).toFixed(1),
          content: row.content.substring(0, 1000) + (row.content.length > 1000 ? '...' : '')
        }));
        
        console.log(`✅ Found ${guidelines.length} relevant guidelines`);
        
        return JSON.stringify({
          query,
          results_count: guidelines.length,
          guidelines: guidelines
        }, null, 2);
        
      } finally {
        await client.end();
      }
      
    } catch (error) {
      console.error('❌ Error searching guidelines:', error);
      return `Error searching guidelines: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
});

// Tool: Get procedure information
const getProcedureInfoTool = tool({
  name: 'get_procedure_info',
  description: 'Get detailed information about dental procedures including CDT codes, descriptions, and categories.',
  parameters: z.object({
    procedure: z.string().describe('Procedure name or CDT code (e.g., "crown", "D2750", "root canal")'),
  }),
  execute: async ({ procedure }) => {
    console.log(`🦷 Looking up procedure: "${procedure}"`);
    
    try {
      const client = createClient();
      await client.connect();
      
      try {
        const result = await client.query(`
          SELECT 
            cdt_code,
            name,
            description,
            category
          FROM procedures
          WHERE 
            LOWER(name) LIKE LOWER('%' || $1 || '%')
            OR LOWER(cdt_code) LIKE LOWER('%' || $1 || '%')
            OR LOWER(description) LIKE LOWER('%' || $1 || '%')
          ORDER BY 
            CASE 
              WHEN LOWER(cdt_code) = LOWER($1) THEN 1
              WHEN LOWER(name) = LOWER($1) THEN 2
              ELSE 3
            END
          LIMIT 5;
        `, [procedure]);
        
        if (result.rows.length === 0) {
          return `No procedures found matching "${procedure}". Try searching with different terms.`;
        }
        
        const procedures = result.rows.map(row => ({
          code: row.cdt_code,
          name: row.name,
          description: row.description,
          category: row.category
        }));
        
        console.log(`✅ Found ${procedures.length} matching procedures`);
        
        return JSON.stringify({
          search_term: procedure,
          procedures: procedures
        }, null, 2);
        
      } finally {
        await client.end();
      }
      
    } catch (error) {
      console.error('❌ Error looking up procedure:', error);
      return `Error looking up procedure: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
});

// Tool: Get insurance carrier information
const getCarrierInfoTool = tool({
  name: 'get_carrier_info',
  description: 'Get information about insurance carriers including contact details and coverage specifics.',
  parameters: z.object({
    carrier: z.string().describe('Insurance carrier name (e.g., "Delta Dental", "Cigna", "Aetna")'),
  }),
  execute: async ({ carrier }) => {
    console.log(`🏥 Looking up carrier: "${carrier}"`);
    
    try {
      const client = createClient();
      await client.connect();
      
      try {
        const result = await client.query(`
          SELECT
            carrier_name,
            payer_id,
            website,
            contact_info
          FROM insurance_carriers
          WHERE
            LOWER(carrier_name) LIKE LOWER('%' || $1 || '%')
            OR LOWER(payer_id) LIKE LOWER('%' || $1 || '%')
          ORDER BY
            CASE
              WHEN LOWER(carrier_name) = LOWER($1) THEN 1
              WHEN LOWER(payer_id) = LOWER($1) THEN 2
              ELSE 3
            END
          LIMIT 5;
        `, [carrier]);
        
        if (result.rows.length === 0) {
          return `No insurance carriers found matching "${carrier}". Try searching with different terms.`;
        }
        
        const carriers = result.rows.map(row => ({
          name: row.name,
          code: row.code,
          website: row.website,
          contact_info: row.contact_info
        }));
        
        console.log(`✅ Found ${carriers.length} matching carriers`);
        
        return JSON.stringify({
          search_term: carrier,
          carriers: carriers
        }, null, 2);
        
      } finally {
        await client.end();
      }
      
    } catch (error) {
      console.error('❌ Error looking up carrier:', error);
      return `Error looking up carrier: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
});

// Create the Dental Narrator Agent
export const dentalNarratorAgent = new Agent({
  name: 'Dental Narrator Agent',
  instructions: `You are a professional dental narrative generator and insurance documentation specialist. Your role is to help dental professionals create accurate, comprehensive narratives for insurance claims and documentation.

Key Responsibilities:
1. **Generate Professional Narratives**: Create detailed, medically accurate dental narratives based on chart notes and clinical findings
2. **Insurance Guidance**: Provide specific coverage information and requirements for different insurance carriers
3. **Procedure Documentation**: Help with proper documentation requirements for various dental procedures
4. **CDT Code Assistance**: Provide accurate CDT codes and procedure descriptions

Guidelines:
- Always use proper dental terminology and medical language
- Include relevant clinical findings and treatment rationale
- Specify insurance carrier requirements when applicable
- Ensure narratives are suitable for insurance submission
- Be thorough but concise in documentation
- Use the search tools to find specific guidelines and requirements

When generating narratives:
1. First search for relevant guidelines using the carrier and procedure information
2. Include patient presentation, clinical findings, treatment provided, and prognosis
3. Justify medical necessity when required
4. Follow insurance-specific documentation requirements
5. Use appropriate CDT codes and procedure descriptions`,
  
  tools: [
    searchGuidelinesTool,
    getProcedureInfoTool,
    getCarrierInfoTool
  ],
});

// Helper function to run the agent
export async function runDentalNarrator(userInput: string) {
  console.log('🤖 Starting Dental Narrator Agent...');
  
  try {
    const result = await run(dentalNarratorAgent, userInput);
    return result.finalOutput;
  } catch (error) {
    console.error('❌ Error running dental narrator:', error);
    throw error;
  }
}

// Export for use in other modules
export { searchGuidelinesTool, getProcedureInfoTool, getCarrierInfoTool };
