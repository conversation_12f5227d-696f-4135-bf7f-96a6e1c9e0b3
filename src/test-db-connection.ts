import { Client } from 'pg';
import { PgVector } from '@mastra/pg';
import { embed } from 'ai';
import { openai } from '@ai-sdk/openai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

async function testDatabaseConnection() {
  try {
    console.log('Testing Supabase pooler connection...');
    
    // Get the connection string
    const connectionString = process.env.POSTGRES_CONNECTION_STRING;
    if (!connectionString) {
      throw new Error('POSTGRES_CONNECTION_STRING environment variable is not set');
    }
    
    // Mask the password for logging
    const maskedConnectionString = connectionString.replace(/:[^:@]+@/, ':****@');
    console.log(`Connection string: ${maskedConnectionString}`);
    
    // Initialize PgVector
    console.log('Initializing PgVector with pooler connection...');
    const pgVector = new PgVector(connectionString);
    
    // Step 1: Basic connection test with native pg client
    console.log('\nStep 1: Testing basic PostgreSQL connection...');
    const client = new Client({
      connectionString,
      connectionTimeoutMillis: 10000,
      ssl: {
        rejectUnauthorized: false
      }
    });
    
    await client.connect();
    console.log('✓ Connection successful!');
    
    const versionResult = await client.query('SELECT version()');
    console.log(`✓ PostgreSQL version: ${versionResult.rows[0].version.split(',')[0]}`);
    await client.end();
    
    // Step 2: Test if we can create or access the vector index
    console.log('\nStep 2: Testing vector index operations...');
    try {
      console.log('Checking for existing indexes...');
      const indexes = await pgVector.listIndexes();
      console.log('✓ Available indexes:', indexes);
      
      if (indexes.includes('insurance_guidelines')) {
        console.log('✓ The insurance_guidelines index exists!');
        
        // Step 3: Test vector query
        console.log('\nStep 3: Testing vector query...');
        const { embedding: testEmbedding } = await embed({
          value: 'dental insurance requirements for crowns',
          model: openai.embedding('text-embedding-3-small'),
        });
        
        const results = await pgVector.query({
          indexName: 'insurance_guidelines',
          queryVector: testEmbedding,
          topK: 2
        });
        
        console.log(`✓ Query successful! Retrieved ${results.length} results`);
        if (results.length > 0) {
          console.log('Sample result content:');
          results.forEach((result, i) => {
            console.log(`  [${i+1}] ${result.metadata?.text?.substring(0, 150)}...`);
          });
        }
      } else {
        console.log('The insurance_guidelines index does not exist yet.');
        console.log('You need to run the initialization first with: npm run init');
      }
    } catch (error: any) {
      console.error('Error with vector operations:', error.message || String(error));
    }
    
    console.log('\nDatabase connection test completed successfully!');
    
  } catch (error: any) {
    console.error('Database connection test failed:', error.message || String(error));
  }
}

// Run the test
testDatabaseConnection().catch(console.error); 